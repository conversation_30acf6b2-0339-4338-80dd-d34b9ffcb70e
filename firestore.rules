rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read access to mysteries for all users
    match /mysteries/{mysteryId} {
      allow read: if true;
      allow write: if request.auth != null; // Only authenticated admin can write mysteries
    }

    // Allow anonymous voting with validation
    match /votes/{voteId} {
      allow read: if true; // Anyone can read voting statistics
      allow create: if isValidVote(); // Anyone can create votes with validation
      allow update, delete: if false; // Prevent vote manipulation
    }

    // Validation function for votes
    function isValidVote() {
      let data = request.resource.data;
      return data.keys().hasAll(['articleId', 'suspectId', 'timestamp', 'userFingerprint']) &&
             data.keys().hasOnly(['articleId', 'suspectId', 'timestamp', 'userFingerprint']) &&
             data.articleId is string &&
             data.suspectId is string &&
             data.timestamp is timestamp &&
             data.userFingerprint is string &&
             data.articleId.size() > 0 &&
             data.suspectId.size() > 0 &&
             data.userFingerprint.size() > 0;
    }

    // Fallback rule for other documents (admin only)
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}