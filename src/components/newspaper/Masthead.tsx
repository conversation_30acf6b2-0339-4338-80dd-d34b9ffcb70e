interface MastheadProps {
  title: string;
  tagline: string;
  date?: string;
  price?: string;
}

export function Masthead({ title, tagline, date, price }: MastheadProps) {
  const currentDate = date || new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <header className="newspaper-masthead px-2 xs:px-0">
      <h1 className="newspaper-name">{title}</h1>
      <p className="newspaper-tagline px-2 xs:px-0">{tagline}</p>
      <div className="newspaper-date-price px-2 xs:px-0">
        <span>{currentDate}</span>
        {price && <span>Price: {price}</span>}
      </div>
    </header>
  );
}
