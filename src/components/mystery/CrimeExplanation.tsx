import type { LegacyCrimeExplanation as CrimeExplanationType } from '@/types/mystery';
import { QuoteBox, Byline } from '@/components/newspaper';
import { CheckCircle, AlertTriangle } from 'lucide-react';
import { <PERSON>down<PERSON>enderer } from '@/components/ui/MarkdownRenderer';

interface CrimeExplanationProps {
  explanation: CrimeExplanationType;
  killerName: string;
  isCorrectGuess: boolean | null;
  accusedName?: string | null;
}

export function CrimeExplanation({ explanation, killerName, isCorrectGuess, accusedName }: CrimeExplanationProps) {
  return (
    <article className="w-full mt-6 sm:mt-8 border-t-4 border-primary pt-6 sm:pt-8">
      <header className="mb-4 sm:mb-6">
        <h2 className="newspaper-headline text-2xl xs:text-3xl md:text-4xl mb-3 sm:mb-4 px-1 xs:px-0">
          The Whole Story Unveiled
        </h2>
        <h3 className="newspaper-subheadline text-base xs:text-lg md:text-xl mb-3 sm:mb-4 px-1 xs:px-0">
          Detective work concludes as the truth behind the mysterious case comes to light
        </h3>
        <Byline
          author="Detective Bureau"
          title="Final Report"
          className="mb-4 sm:mb-6 px-1 xs:px-0"
        />

        {isCorrectGuess !== null && (
          <div className="mb-4 sm:mb-6 p-3 sm:p-4 border-l-4 border-primary bg-gray-50 mx-1 xs:mx-0">
            <div className="flex items-start xs:items-center">
              {isCorrectGuess ? (
                <>
                  <CheckCircle className="h-5 w-5 xs:h-6 xs:w-6 text-green-600 mr-2 xs:mr-3 flex-shrink-0 mt-0.5 xs:mt-0" />
                  <span className="text-green-700 font-semibold newspaper-text text-sm xs:text-base">
                    Brilliant deduction! You correctly identified {killerName} as the culprit.
                  </span>
                </>
              ) : (
                <>
                  <AlertTriangle className="h-5 w-5 xs:h-6 xs:w-6 text-red-600 mr-2 xs:mr-3 flex-shrink-0 mt-0.5 xs:mt-0" />
                  <span className="text-red-700 font-semibold newspaper-text text-sm xs:text-base">
                    {accusedName ? `Your accusation of ${accusedName} was incorrect. The real culprit was ${killerName}.` : `The real culprit was ${killerName}.`}
                  </span>
                </>
              )}
            </div>
          </div>
        )}
      </header>

      <div className="space-y-4 sm:space-y-6 px-1 xs:px-0">
        <div>
          <h4 className="newspaper-sidebar-headline text-lg xs:text-xl mb-2 sm:mb-3">The Motive</h4>
          <MarkdownRenderer content={explanation.motive} />
        </div>

        <QuoteBox attribution="Detective's Analysis">
          The method employed in this case reveals a calculated approach that required both opportunity and specific knowledge.
        </QuoteBox>

        <div>
          <h4 className="newspaper-sidebar-headline text-lg xs:text-xl mb-2 sm:mb-3">The Method</h4>
          <MarkdownRenderer content={explanation.method} />
        </div>

        <div>
          <h4 className="newspaper-sidebar-headline text-lg xs:text-xl mb-2 sm:mb-3">Key Evidence</h4>
          <MarkdownRenderer content={explanation.keyEvidence} />
        </div>

        <div className="border-t border-gray-300 pt-6 mt-8">
          <p className="text-sm italic text-gray-600">
            Case closed. Justice has been served, and the truth has prevailed once again in our fair city.
          </p>
        </div>
      </div>
    </article>
  );
}
