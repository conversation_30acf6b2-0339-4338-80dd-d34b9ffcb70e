'use client';

import type { LegacySuspect, VoteStatistics } from '@/types/mystery';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { UserIcon, Loader2 } from 'lucide-react';
import { CommunityResults } from './CommunityResults';

interface SuspectListProps {
  suspects: LegacySuspect[];
  onSelectSuspect: (suspectId: string) => void;
  onConfirmAccusation: () => void;
  selectedSuspectId: string | null;
  isAccusationMade: boolean;
  // Voting-related props
  votingStatistics?: VoteStatistics | null;
  isLoadingVotes?: boolean;
  votingError?: string | null;
  showCommunityResults?: boolean;
}

export function SuspectList({
  suspects,
  onSelectSuspect,
  onConfirmAccusation,
  selectedSuspectId,
  isAccusationMade,
  votingStatistics,
  isLoadingVotes = false,
  votingError = null,
  showCommunityResults = false,
}: SuspectListProps) {
  const selectedSuspectName = suspects.find(s => s.id === selectedSuspectId)?.name;

  // Show community results if accusation is made and we want to show results
  if (isAccusationMade && showCommunityResults) {
    return (
      <CommunityResults
        statistics={votingStatistics}
        userSelectedSuspectId={selectedSuspectId}
        isLoading={isLoadingVotes}
        error={votingError}
      />
    );
  }

  return (
    <section aria-labelledby="suspects-heading" className="w-full">
      <header className="mb-4 sm:mb-6 px-1 xs:px-0">
        <h3 className="newspaper-sidebar-headline flex items-center gap-2 text-lg xs:text-xl">
          <UserIcon className="h-4 w-4 xs:h-5 xs:w-5" />
          The Usual Suspects
        </h3>
        <p className="newspaper-sidebar-text text-xs xs:text-sm italic">
          {isAccusationMade ? "The investigation is closed." : "One of them is the culprit. Who will you accuse?"}
        </p>
      </header>

      <div className="space-y-3 sm:space-y-4 mb-4 sm:mb-6 px-1 xs:px-0">
        {suspects.map((suspect) => (
          <div
            key={suspect.id}
            className={cn(
              'border-b border-gray-300 pb-3 sm:pb-4 cursor-pointer transition-all duration-200',
              selectedSuspectId === suspect.id && 'bg-gray-100 p-2 sm:p-3 border-2 border-primary rounded-sm',
              isAccusationMade && selectedSuspectId !== suspect.id && 'opacity-60',
              isAccusationMade && selectedSuspectId === suspect.id && 'bg-gray-100 p-2 sm:p-3 border-2 border-primary rounded-sm'
            )}
            role="button"
            tabIndex={isAccusationMade ? -1 : 0}
            aria-pressed={selectedSuspectId === suspect.id}
            onClick={() => !isAccusationMade && onSelectSuspect(suspect.id)}
            onKeyDown={(e) => {
              if (!isAccusationMade && (e.key === 'Enter' || e.key === ' ')) {
                onSelectSuspect(suspect.id);
              }
            }}
          >
            <h4 className="newspaper-sidebar-headline text-lg mb-2">
              {suspect.name}
            </h4>
            <p className="newspaper-sidebar-text text-sm">
              {suspect.description}
            </p>
          </div>
        ))}
      </div>

      {selectedSuspectId && !isAccusationMade && (
        <div className="border-t border-gray-300 pt-3 sm:pt-4 px-1 xs:px-0">
          <div className="newspaper-sidebar-text">
            <h4 className="newspaper-sidebar-headline text-sm xs:text-base mb-2">Make Your Accusation</h4>
            <p className="text-xs xs:text-sm mb-2 sm:mb-3">
              You've selected <strong>{selectedSuspectName}</strong> as your prime suspect.
            </p>
            <p className="text-xs italic mb-3 sm:mb-4 text-gray-600">
              Once you confirm, the truth will be revealed...
            </p>
            <Button
              onClick={onConfirmAccusation}
              size="sm"
              disabled={isLoadingVotes}
              className="w-full bg-primary hover:bg-primary/90 text-primary-foreground text-xs xs:text-sm py-2 disabled:opacity-50"
            >
              {isLoadingVotes ? (
                <>
                  <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                `Accuse ${selectedSuspectName}`
              )}
            </Button>
          </div>
        </div>
      )}
    </section>
  );
}
