'use client';

import type { VoteStatistics } from '@/types/mystery';
import { cn } from '@/lib/utils';
import { Users, TrendingUp, CheckCircle } from 'lucide-react';

interface CommunityResultsProps {
  statistics?: VoteStatistics | null;
  userSelectedSuspectId?: string | null;
  isLoading?: boolean;
  error?: string | null;
}

export function CommunityResults({ 
  statistics, 
  userSelectedSuspectId, 
  isLoading = false,
  error = null 
}: CommunityResultsProps) {
  if (isLoading) {
    return (
      <section aria-labelledby="community-results-heading" className="w-full">
        <header className="mb-4 sm:mb-6 px-1 xs:px-0">
          <h3 className="newspaper-sidebar-headline flex items-center gap-2 text-lg xs:text-xl">
            <Users className="h-4 w-4 xs:h-5 xs:w-5 animate-pulse" />
            Community Poll
          </h3>
          <p className="newspaper-sidebar-text text-xs xs:text-sm italic">
            Loading community results...
          </p>
        </header>
        <div className="space-y-3 px-1 xs:px-0">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-2 bg-gray-100 rounded"></div>
            </div>
          ))}
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section aria-labelledby="community-results-heading" className="w-full">
        <header className="mb-4 sm:mb-6 px-1 xs:px-0">
          <h3 className="newspaper-sidebar-headline flex items-center gap-2 text-lg xs:text-xl">
            <Users className="h-4 w-4 xs:h-5 xs:w-5" />
            Community Poll
          </h3>
          <p className="newspaper-sidebar-text text-xs xs:text-sm italic text-red-600">
            Unable to load community results
          </p>
        </header>
      </section>
    );
  }

  if (!statistics) {
    return (
      <section aria-labelledby="community-results-heading" className="w-full">
        <header className="mb-4 sm:mb-6 px-1 xs:px-0">
          <h3 className="newspaper-sidebar-headline flex items-center gap-2 text-lg xs:text-xl">
            <Users className="h-4 w-4 xs:h-5 xs:w-5" />
            Community Poll
          </h3>
          <p className="newspaper-sidebar-text text-xs xs:text-sm italic">
            No voting data available
          </p>
        </header>
      </section>
    );
  }

  const { totalVotes, suspectVotes } = statistics;
  
  // Sort suspects by vote percentage (highest first)
  const sortedSuspects = [...suspectVotes].sort((a, b) => b.percentage - a.percentage);

  return (
    <section aria-labelledby="community-results-heading" className="w-full">
      <header className="mb-4 sm:mb-6 px-1 xs:px-0">
        <h3 className="newspaper-sidebar-headline flex items-center gap-2 text-lg xs:text-xl">
          <Users className="h-4 w-4 xs:h-5 xs:w-5" />
          Community Poll
        </h3>
        <p className="newspaper-sidebar-text text-xs xs:text-sm italic">
          {totalVotes === 0 
            ? "Be the first to vote!" 
            : `${totalVotes.toLocaleString()} ${totalVotes === 1 ? 'detective has' : 'detectives have'} weighed in`
          }
        </p>
      </header>

      {totalVotes > 0 && (
        <div className="space-y-3 sm:space-y-4 px-1 xs:px-0">
          {sortedSuspects.map((suspect, index) => {
            const isUserSelection = userSelectedSuspectId === suspect.suspectId;
            const isCorrectAnswer = suspect.isCorrectAnswer;
            const isTopChoice = index === 0 && suspect.percentage > 0;
            
            return (
              <div
                key={suspect.suspectId}
                className={cn(
                  'border-b border-gray-300 pb-3 sm:pb-4 transition-all duration-200',
                  isCorrectAnswer && 'bg-green-50 p-2 sm:p-3 border-2 border-green-300 rounded-sm',
                  isUserSelection && !isCorrectAnswer && 'bg-blue-50 p-2 sm:p-3 border-2 border-blue-300 rounded-sm'
                )}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className={cn(
                    "newspaper-sidebar-headline text-base sm:text-lg flex items-center gap-2",
                    isCorrectAnswer && "text-green-700"
                  )}>
                    {suspect.suspectName}
                    {isCorrectAnswer && (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    )}
                    {isTopChoice && !isCorrectAnswer && (
                      <TrendingUp className="h-4 w-4 text-blue-600" />
                    )}
                  </h4>
                  <span className={cn(
                    "newspaper-sidebar-text font-semibold text-sm",
                    isCorrectAnswer && "text-green-700"
                  )}>
                    {suspect.percentage}%
                  </span>
                </div>
                
                {/* Progress bar */}
                <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                  <div
                    className={cn(
                      "h-2 rounded-full transition-all duration-500",
                      isCorrectAnswer ? "bg-green-500" : "bg-blue-500"
                    )}
                    style={{ width: `${suspect.percentage}%` }}
                  />
                </div>
                
                <div className="flex items-center justify-between text-xs">
                  <span className="newspaper-sidebar-text">
                    {suspect.voteCount.toLocaleString()} {suspect.voteCount === 1 ? 'vote' : 'votes'}
                  </span>
                  
                  {isUserSelection && (
                    <span className="text-blue-600 font-medium">
                      Your choice
                    </span>
                  )}
                  
                  {isCorrectAnswer && (
                    <span className="text-green-600 font-medium">
                      Correct answer
                    </span>
                  )}
                </div>
              </div>
            );
          })}
          
          <div className="border-t border-gray-300 pt-3 sm:pt-4 mt-4">
            <p className="newspaper-sidebar-text text-xs italic text-gray-600 text-center">
              Results update in real-time as more detectives participate
            </p>
          </div>
        </div>
      )}
    </section>
  );
}
