import type { Vote, VoteStatistics, SuspectVoteCount, Mystery } from '@/types/mystery';
import { db } from './firebase';
import { 
  collection, 
  addDoc, 
  query, 
  where, 
  getDocs, 
  orderBy,
  Timestamp 
} from 'firebase/firestore';

// Generate anonymous user fingerprint for privacy
export const generateUserFingerprint = (): string => {
  try {
    // Create a simple fingerprint based on browser characteristics
    // This is anonymous and doesn't store PII
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    let canvasFingerprint = '';

    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Daily Deduction Fingerprint', 2, 2);
      canvasFingerprint = canvas.toDataURL();
    }

    const fingerprint = [
      navigator.userAgent || 'unknown',
      navigator.language || 'unknown',
      (screen.width || 0) + 'x' + (screen.height || 0),
      new Date().getTimezoneOffset() || 0,
      canvasFingerprint
    ].join('|');

    // Simple hash function to create shorter identifier
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    return Math.abs(hash).toString(36);
  } catch (error) {
    console.warn('Error generating fingerprint, using fallback:', error);
    // Fallback to a simple random identifier
    return Math.random().toString(36).substring(2, 15);
  }
};

// Check if user has already voted for this article
export const hasUserVoted = async (articleId: string, userFingerprint: string): Promise<boolean> => {
  try {
    const votesRef = collection(db, 'votes');
    const q = query(
      votesRef,
      where('articleId', '==', articleId),
      where('userFingerprint', '==', userFingerprint)
    );
    
    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  } catch (error) {
    console.error('Error checking if user has voted:', error);
    return false; // Allow voting if check fails
  }
};

// Submit a vote to Firestore
export const submitVote = async (
  articleId: string, 
  suspectId: string, 
  userFingerprint: string
): Promise<void> => {
  try {
    // Check if user has already voted
    const alreadyVoted = await hasUserVoted(articleId, userFingerprint);
    if (alreadyVoted) {
      console.log('User has already voted for this article');
      return; // Silently ignore duplicate votes
    }

    const vote: Omit<Vote, 'id'> = {
      articleId,
      suspectId,
      timestamp: new Date(),
      userFingerprint
    };

    const votesRef = collection(db, 'votes');
    await addDoc(votesRef, {
      ...vote,
      timestamp: Timestamp.fromDate(vote.timestamp)
    });
    
    console.log('Vote submitted successfully');
  } catch (error) {
    console.error('Error submitting vote:', error);
    throw new Error('Failed to submit vote. Please try again.');
  }
};

// Get voting statistics for an article
export const getVotingStatistics = async (
  articleId: string, 
  mystery: Mystery
): Promise<VoteStatistics> => {
  try {
    const votesRef = collection(db, 'votes');
    const q = query(
      votesRef,
      where('articleId', '==', articleId),
      orderBy('timestamp', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    const votes = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      timestamp: doc.data().timestamp.toDate()
    })) as Vote[];

    // Count votes for each suspect
    const suspectVoteCounts = new Map<string, number>();
    votes.forEach(vote => {
      const currentCount = suspectVoteCounts.get(vote.suspectId) || 0;
      suspectVoteCounts.set(vote.suspectId, currentCount + 1);
    });

    const totalVotes = votes.length;
    
    // Create statistics for each suspect
    const suspectVotes: SuspectVoteCount[] = mystery.suspects.map(suspect => {
      const voteCount = suspectVoteCounts.get(suspect.id) || 0;
      const percentage = totalVotes > 0 ? Math.round((voteCount / totalVotes) * 100) : 0;
      
      return {
        suspectId: suspect.id,
        suspectName: suspect.name,
        voteCount,
        percentage,
        isCorrectAnswer: suspect.id === mystery.killerId
      };
    });

    return {
      articleId,
      totalVotes,
      suspectVotes
    };
  } catch (error) {
    console.error('Error getting voting statistics:', error);
    throw new Error('Failed to load voting statistics. Please try again.');
  }
};

// Get user's vote for an article (if any)
export const getUserVote = async (
  articleId: string, 
  userFingerprint: string
): Promise<Vote | null> => {
  try {
    const votesRef = collection(db, 'votes');
    const q = query(
      votesRef,
      where('articleId', '==', articleId),
      where('userFingerprint', '==', userFingerprint)
    );
    
    const querySnapshot = await getDocs(q);
    if (querySnapshot.empty) {
      return null;
    }
    
    const doc = querySnapshot.docs[0];
    return {
      id: doc.id,
      ...doc.data(),
      timestamp: doc.data().timestamp.toDate()
    } as Vote;
  } catch (error) {
    console.error('Error getting user vote:', error);
    return null;
  }
};
