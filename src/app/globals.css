@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'Crimson Text', serif; /* Newspaper body font */
  background: linear-gradient(45deg, #f8f6f0 25%, #f5f3ed 25%, #f5f3ed 50%, #f8f6f0 50%, #f8f6f0 75%, #f5f3ed 75%);
  background-size: 4px 4px;
  position: relative;
}

/* Paper texture overlay */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 108, 0.3) 3px, transparent 3px),
    radial-gradient(circle at 80% 20%, rgba(120, 119, 108, 0.3) 3px, transparent 3px),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 108, 0.2) 2px, transparent 2px);
  background-size: 200px 200px, 180px 180px, 100px 100px;
  pointer-events: none;
  opacity: 0.4;
  z-index: 1;
}

@layer base {
  :root {
    /* Newspaper color scheme */
    --background: 48 56% 96%; /* Newspaper off-white #f8f6f0 */
    --foreground: 0 0% 17%; /* Dark newspaper text #2c2c2c */
    --card: 48 56% 95%; /* Slightly different from background for depth */
    --card-foreground: 0 0% 17%;
    --popover: 48 56% 96%;
    --popover-foreground: 0 0% 17%;
    --primary: 0 0% 17%; /* Dark newspaper text #2c2c2c */
    --primary-foreground: 0 0% 98%; /* Light color for text on primary */
    --secondary: 0 0% 33%; /* Medium gray for secondary elements */
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 80%; /* Light gray for muted elements */
    --muted-foreground: 0 0% 40%; /* Medium gray for muted text */
    --accent: 51 100% 50%; /* Gold accent for highlights */
    --accent-foreground: 0 0% 17%; /* Dark text on gold */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 80%; /* Light gray borders */
    --input: 0 0% 85%;
    --ring: 0 0% 17%; /* Dark ring color */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0rem; /* No rounded corners for newspaper look */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    /* Dark theme can be defined here if needed, but current proposal is light-themed */
    --background: 0 0% 10%; /* Dark background for a potential dark mode */
    --foreground: 0 0% 95%;
    --card: 0 0% 12%;
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 10%;
    --popover-foreground: 0 0% 95%;
    --primary: 345 80% 55%; /* Brighter burgundy for dark mode */
    --primary-foreground: 0 0% 98%;
    --secondary: 345 50% 35%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 60%;
    --accent: 51 100% 60%; /* Slightly brighter gold */
    --accent-foreground: 345 100% 15%;
    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 98%;
    --border: 345 20% 40%;
    --input: 345 20% 30%;
    --ring: 51 100% 60%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    /* Ensure scrolling behavior is smooth for better UX */
    scroll-behavior: smooth;
    line-height: 1.6;
    color: #2c2c2c;
  }
  /* Styling for selection to match accent */
  ::selection {
    background-color: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
  }
}

/* Newspaper-specific styles */
@layer components {
  .newspaper-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 40px 20px;
    background: rgba(248, 246, 240, 0.95);
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
  }

  .newspaper-headline {
    font-family: 'Playfair Display', serif;
    font-weight: 900;
    line-height: 1.1;
    color: #1a1a1a;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  }

  .newspaper-subheadline {
    font-style: italic;
    color: #555;
    font-weight: 400;
  }

  .newspaper-byline {
    font-size: 0.9rem;
    color: #666;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .newspaper-text {
    font-size: 1.1rem;
    line-height: 1.7;
    text-align: justify;
    hyphens: auto;
  }

  .newspaper-text p {
    margin-bottom: 15px;
    text-indent: 1.5em;
  }

  .newspaper-text p:first-of-type {
    text-indent: 0;
  }

  .newspaper-drop-cap::first-letter {
    float: left;
    font-family: 'Playfair Display', serif;
    font-size: 4rem;
    line-height: 3rem;
    margin: 5px 8px 0 0;
    font-weight: 900;
    color: #2c2c2c;
  }

  .newspaper-quote-box {
    background: rgba(0, 0, 0, 0.05);
    border-left: 4px solid #2c2c2c;
    padding: 20px;
    margin: 25px 0;
    font-style: italic;
    font-size: 1.05rem;
    position: relative;
  }

  .newspaper-quote-box::before {
    content: '"';
    font-size: 4rem;
    font-family: 'Playfair Display', serif;
    position: absolute;
    top: -10px;
    left: 10px;
    color: #ccc;
    line-height: 1;
  }

  .newspaper-masthead {
    text-align: center;
    border-bottom: 4px solid #2c2c2c;
    border-top: 2px solid #2c2c2c;
    padding: 20px 0;
    margin-bottom: 30px;
    background: linear-gradient(to bottom, #f8f6f0, #f5f3ed);
  }

  .newspaper-name {
    font-family: 'Playfair Display', serif;
    font-size: 3.5rem;
    font-weight: 900;
    letter-spacing: 2px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
  }

  .newspaper-tagline {
    font-size: 0.9rem;
    font-style: italic;
    color: #666;
    letter-spacing: 1px;
  }

  .newspaper-date-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    font-size: 0.8rem;
    color: #666;
  }

  .newspaper-article-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
  }

  .newspaper-main-article {
    grid-column: 1 / 3;
  }

  .newspaper-sidebar {
    grid-column: 3;
    border-left: 1px solid #ccc;
    padding-left: 20px;
  }

  .newspaper-sidebar-headline {
    font-family: 'Playfair Display', serif;
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: #2c2c2c;
  }

  .newspaper-sidebar-text {
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 20px;
  }

  /* Additional polish and accessibility */
  .newspaper-focus-ring:focus {
    outline: 2px solid #2c2c2c;
    outline-offset: 2px;
  }

  .newspaper-hover-effect:hover {
    background-color: rgba(0, 0, 0, 0.02);
    transition: background-color 0.2s ease;
  }

  .newspaper-divider {
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, #ccc 20%, #ccc 80%, transparent);
    margin: 2rem 0;
  }

  /* Smooth transitions for interactive elements */
  .newspaper-interactive {
    transition: all 0.2s ease-in-out;
  }

  .newspaper-interactive:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* Enhanced readability */
  .newspaper-text strong {
    font-weight: 600;
    color: #1a1a1a;
  }

  .newspaper-text em {
    font-style: italic;
    color: #444;
  }

  /* Better spacing for lists if needed */
  .newspaper-text ul,
  .newspaper-text ol {
    margin: 1rem 0;
    padding-left: 2rem;
  }

  .newspaper-text li {
    margin-bottom: 0.5rem;
  }

  /* Responsive newspaper styles */
  @media (max-width: 768px) {
    .newspaper-name {
      font-size: 2.5rem;
      letter-spacing: 1px;
    }

    .newspaper-article-container {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    .newspaper-main-article, .newspaper-sidebar {
      grid-column: 1;
    }

    .newspaper-sidebar {
      border-left: none;
      border-top: 1px solid #ccc;
      padding-left: 0;
      padding-top: 20px;
    }

    .newspaper-headline {
      font-size: 2.2rem;
      line-height: 1.2;
    }

    .newspaper-container {
      padding: 24px 20px; /* Increased from 20px 15px for better mobile readability */
    }

    .newspaper-drop-cap::first-letter {
      font-size: 3rem;
      line-height: 2.5rem;
      margin: 3px 6px 0 0;
    }

    .newspaper-text {
      font-size: 1rem;
      line-height: 1.6;
    }

    .newspaper-quote-box {
      padding: 15px;
      margin: 20px 0;
      font-size: 1rem;
    }

    .newspaper-quote-box::before {
      font-size: 3rem;
      top: -8px;
      left: 8px;
    }
  }

  /* Enhanced mobile breakpoints for better responsive design */
  @media (max-width: 480px) {
    .newspaper-name {
      font-size: 2rem;
    }

    .newspaper-headline {
      font-size: 1.8rem;
    }

    .newspaper-container {
      padding: 20px 16px; /* Increased from 15px 10px for better mobile readability */
    }

    .newspaper-masthead {
      padding: 15px 0;
      margin-bottom: 20px;
    }

    .newspaper-date-price {
      flex-direction: column;
      gap: 5px;
      text-align: center;
    }
  }

  /* Extra small mobile devices (320px and below) */
  @media (max-width: 320px) {
    .newspaper-name {
      font-size: 1.8rem;
      letter-spacing: 0.5px;
    }

    .newspaper-headline {
      font-size: 1.6rem;
      line-height: 1.3;
    }

    .newspaper-container {
      padding: 16px 16px; /* Maintain adequate padding even on very small screens */
    }

    .newspaper-text {
      font-size: 0.95rem;
      line-height: 1.5;
    }

    .newspaper-quote-box {
      padding: 12px;
      margin: 16px 0;
      font-size: 0.95rem;
    }
  }

  /* Newspaper Markdown Styles */
  .newspaper-markdown {
    font-size: 1.1rem;
    line-height: 1.7;
    text-align: justify;
    hyphens: auto;
  }

  .newspaper-markdown h1,
  .newspaper-markdown h2,
  .newspaper-markdown h3,
  .newspaper-markdown h4,
  .newspaper-markdown h5,
  .newspaper-markdown h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    color: #1a1a1a;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    line-height: 1.2;
  }

  .newspaper-markdown h1 {
    font-size: 2.2rem;
    font-weight: 900;
    border-bottom: 2px solid #2c2c2c;
    padding-bottom: 0.3em;
  }

  .newspaper-markdown h2 {
    font-size: 1.8rem;
    font-weight: 800;
    border-bottom: 1px solid #666;
    padding-bottom: 0.2em;
  }

  .newspaper-markdown h3 {
    font-size: 1.4rem;
    font-weight: 700;
  }

  .newspaper-markdown h4 {
    font-size: 1.2rem;
    font-weight: 700;
  }

  .newspaper-markdown h5,
  .newspaper-markdown h6 {
    font-size: 1.1rem;
    font-weight: 600;
  }

  .newspaper-markdown p {
    margin-bottom: 1em;
    text-indent: 1.5em;
  }

  .newspaper-markdown p:first-child {
    text-indent: 0;
  }

  .newspaper-markdown strong {
    font-weight: 700;
    color: #1a1a1a;
  }

  .newspaper-markdown em {
    font-style: italic;
    color: #333;
  }

  .newspaper-markdown a {
    color: #2c2c2c;
    text-decoration: underline;
    text-decoration-thickness: 1px;
    text-underline-offset: 2px;
    transition: color 0.2s ease;
  }

  .newspaper-markdown a:hover {
    color: #000;
    text-decoration-thickness: 2px;
  }

  .newspaper-markdown ul,
  .newspaper-markdown ol {
    margin: 1em 0;
    padding-left: 2em;
  }

  .newspaper-markdown ul {
    list-style-type: disc;
  }

  .newspaper-markdown ol {
    list-style-type: decimal;
  }

  .newspaper-markdown li {
    margin-bottom: 0.5em;
    text-indent: 0;
  }

  .newspaper-markdown li p {
    text-indent: 0;
    margin-bottom: 0.5em;
  }

  .newspaper-markdown blockquote {
    border-left: 4px solid #2c2c2c;
    margin: 1.5em 0;
    padding: 1em 1.5em;
    background: rgba(0, 0, 0, 0.02);
    font-style: italic;
    position: relative;
  }

  .newspaper-markdown blockquote p {
    text-indent: 0;
    margin-bottom: 0.5em;
  }

  .newspaper-markdown blockquote::before {
    content: '"';
    font-size: 3em;
    font-family: 'Playfair Display', serif;
    color: #ccc;
    position: absolute;
    left: 0.2em;
    top: -0.1em;
    line-height: 1;
  }

  .newspaper-markdown code {
    background: rgba(0, 0, 0, 0.05);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    color: #2c2c2c;
  }

  .newspaper-markdown pre {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 1em;
    margin: 1em 0;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;
  }

  .newspaper-markdown pre code {
    background: none;
    padding: 0;
    border-radius: 0;
    font-size: inherit;
  }

  .newspaper-markdown hr {
    border: none;
    height: 1px;
    background: linear-gradient(to right, transparent, #ccc 20%, #ccc 80%, transparent);
    margin: 2em 0;
  }

  .newspaper-markdown table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
    font-size: 0.95em;
  }

  .newspaper-markdown th,
  .newspaper-markdown td {
    border: 1px solid #ddd;
    padding: 0.5em;
    text-align: left;
  }

  .newspaper-markdown th {
    background: rgba(0, 0, 0, 0.05);
    font-weight: 600;
    font-family: 'Playfair Display', serif;
  }

  .newspaper-markdown tr:nth-child(even) {
    background: rgba(0, 0, 0, 0.02);
  }

  /* Print styles */
  @media print {
    body {
      background: white !important;
      font-size: 12pt;
      line-height: 1.4;
    }

    body::before {
      display: none;
    }

    .newspaper-container {
      box-shadow: none;
      max-width: none;
      margin: 0;
      padding: 0.5in;
      background: white !important;
    }

    .newspaper-masthead {
      border-bottom: 2px solid black;
      border-top: 1px solid black;
      background: white !important;
    }

    .newspaper-name {
      font-size: 24pt;
      color: black !important;
    }

    .newspaper-headline {
      font-size: 18pt;
      color: black !important;
      page-break-after: avoid;
    }

    .newspaper-subheadline {
      font-size: 14pt;
      color: black !important;
    }

    .newspaper-text,
    .newspaper-markdown {
      font-size: 11pt;
      line-height: 1.3;
      text-align: justify;
      color: black !important;
    }

    .newspaper-quote-box {
      border: 1px solid black;
      background: #f5f5f5 !important;
      page-break-inside: avoid;
    }

    .newspaper-article-container {
      grid-template-columns: 2fr 1fr;
      gap: 0.25in;
    }

    .newspaper-sidebar {
      border-left: 1px solid black;
      padding-left: 0.125in;
    }

    /* Avoid page breaks in important sections */
    .newspaper-masthead,
    .newspaper-quote-box,
    .newspaper-sidebar > div {
      page-break-inside: avoid;
    }

    /* Hide interactive elements in print */
    button {
      display: none;
    }

    /* Print-specific markdown styles */
    .newspaper-markdown h1,
    .newspaper-markdown h2,
    .newspaper-markdown h3,
    .newspaper-markdown h4,
    .newspaper-markdown h5,
    .newspaper-markdown h6 {
      color: black !important;
      page-break-after: avoid;
    }

    .newspaper-markdown blockquote {
      border-left: 2px solid black;
      background: #f5f5f5 !important;
      page-break-inside: avoid;
    }

    .newspaper-markdown pre,
    .newspaper-markdown code {
      background: #f5f5f5 !important;
      color: black !important;
    }

    .newspaper-markdown a {
      color: black !important;
      text-decoration: underline;
    }
  }
}
