'use client';

import { useEffect, useState } from 'react';
import type { Mystery } from '@/types/mystery';
import { getDailyMystery, MysteryNotFoundError } from '@/lib/mysteryService';
import { DailyMysteryCard } from '@/components/mystery/DailyMysteryCard';
import { SuspectList } from '@/components/mystery/SuspectList';
import { CrimeExplanation } from '@/components/mystery/CrimeExplanation';
import { NoMysteryAvailable } from '@/components/mystery/NoMysteryAvailable';
import { Masthead, NewspaperContainer, ArticleLayout } from '@/components/newspaper';
import { Loader2 } from 'lucide-react';

export default function HomePage() {
  const [currentMystery, setCurrentMystery] = useState<Mystery | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isConnectionError, setIsConnectionError] = useState(false);

  const [draftSelectedSuspectId, setDraftSelectedSuspectId] = useState<string | null>(null);
  const [finalAccusedSuspectId, setFinalAccusedSuspectId] = useState<string | null>(null);
  const [isCorrectGuess, setIsCorrectGuess] = useState<boolean | null>(null);
  const [showExplanation, setShowExplanation] = useState(false);

  useEffect(() => {
    async function fetchMystery() {
      setIsLoading(true);
      setError(null);
      setIsConnectionError(false);

      try {
        const mystery = await getDailyMystery();
        setCurrentMystery(mystery);
      } catch (error) {
        console.error("Failed to fetch mystery:", error);

        if (error instanceof MysteryNotFoundError) {
          setError(error.message);
          setIsConnectionError(false);
        } else {
          setError(error instanceof Error ? error.message : 'An unexpected error occurred');
          setIsConnectionError(true);
        }
      } finally {
        setIsLoading(false);
      }
    }
    fetchMystery();
  }, []);

  const handleSelectSuspect = (suspectId: string) => {
    setDraftSelectedSuspectId(suspectId);
  };

  const handleConfirmAccusation = () => {
    if (!draftSelectedSuspectId || !currentMystery) return;

    setFinalAccusedSuspectId(draftSelectedSuspectId);
    const correct = draftSelectedSuspectId === currentMystery.killerId;
    setIsCorrectGuess(correct);
    setShowExplanation(true); // Reveal explanation after accusation
    // Optional: scroll to explanation or result
    // document.getElementById('explanation-section')?.scrollIntoView({ behavior: 'smooth' });
  };

  const resetDailyState = () => {
    // This would be more complex with actual daily logic, e.g., tied to date change
    // For now, simple reset for demo purposes if we want a "play again" feel for same mystery
    setDraftSelectedSuspectId(null);
    setFinalAccusedSuspectId(null);
    setIsCorrectGuess(null);
    setShowExplanation(false);
    // Potentially re-fetch if new day logic is implemented in service
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4 md:p-8">
        <NewspaperContainer>
          <div className="text-center">
            <Loader2 className="h-16 w-16 text-primary animate-spin mx-auto" />
            <p className="mt-4 text-xl newspaper-headline text-primary">Unearthing today's mystery...</p>
          </div>
        </NewspaperContainer>
      </div>
    );
  }

  if (error || !currentMystery) {
    return (
      <div className="min-h-screen">
        <NewspaperContainer>
          <Masthead
            title="THE DAILY DEDUCTION"
            tagline="A new enigma unfolds each day. Can you piece together the clues?"
          />

          <main>
            <NoMysteryAvailable
              error={error || undefined}
              isConnectionError={isConnectionError}
            />
          </main>

          <footer className="mt-12 pt-8 border-t border-primary/20 text-center">
            <p className="text-sm text-muted-foreground">
              &copy; {new Date().getFullYear()} The Daily Deduction. The truth is out there.
            </p>
          </footer>
        </NewspaperContainer>
      </div>
    );
  }
  
  const accusedSuspect = currentMystery.suspects.find(s => s.id === finalAccusedSuspectId);

  return (
    <div className="min-h-screen">
      <NewspaperContainer>
        <Masthead
          title="THE DAILY DEDUCTION"
          tagline="A new enigma unfolds each day. Can you piece together the clues?"
        />

        <ArticleLayout
          mainContent={
            <div className="space-y-6 sm:space-y-8">
              <DailyMysteryCard mystery={currentMystery} />

              {showExplanation && (
                <div id="explanation-section">
                  <CrimeExplanation
                    explanation={currentMystery.explanation}
                    killerName={currentMystery.suspects.find(s => s.id === currentMystery.killerId)?.name || 'The Culprit'}
                    isCorrectGuess={isCorrectGuess}
                    accusedName={accusedSuspect?.name}
                  />
                </div>
              )}
            </div>
          }
          sidebarContent={
            !finalAccusedSuspectId ? (
              <SuspectList
                suspects={currentMystery.suspects}
                onSelectSuspect={handleSelectSuspect}
                onConfirmAccusation={handleConfirmAccusation}
                selectedSuspectId={draftSelectedSuspectId}
                isAccusationMade={!!finalAccusedSuspectId}
              />
            ) : undefined
          }
        />

        <footer className="mt-12 pt-8 border-t border-primary/20 text-center">
          <p className="text-sm text-muted-foreground">
            &copy; {new Date().getFullYear()} The Daily Deduction. The truth is out there.
          </p>
        </footer>
      </NewspaperContainer>
    </div>
  );
}
