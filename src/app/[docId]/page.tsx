'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import type { Mystery } from '@/types/mystery';
import { getMysteryByDocId, MysteryNotFoundError } from '@/lib/mysteryService';
import { DailyMysteryCard } from '@/components/mystery/DailyMysteryCard';
import { SuspectList } from '@/components/mystery/SuspectList';
import { CrimeExplanation } from '@/components/mystery/CrimeExplanation';
import { NoMysteryAvailable } from '@/components/mystery/NoMysteryAvailable';
import { Masthead, NewspaperContainer, ArticleLayout } from '@/components/newspaper';
import { Loader2 } from 'lucide-react';

export default function ArticlePage() {
  const params = useParams();
  const docId = params.docId as string;
  
  const [currentMystery, setCurrentMystery] = useState<Mystery | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isConnectionError, setIsConnectionError] = useState(false);

  const [draftSelectedSuspectId, setDraftSelectedSuspectId] = useState<string | null>(null);
  const [finalAccusedSuspectId, setFinalAccusedSuspectId] = useState<string | null>(null);
  const [isCorrectGuess, setIsCorrectGuess] = useState<boolean | null>(null);
  const [showExplanation, setShowExplanation] = useState(false);

  useEffect(() => {
    async function fetchMystery() {
      if (!docId) return;
      
      setIsLoading(true);
      setError(null);
      setIsConnectionError(false);

      try {
        const mystery = await getMysteryByDocId(docId);
        setCurrentMystery(mystery);
      } catch (error) {
        console.error("Failed to fetch mystery:", error);

        if (error instanceof MysteryNotFoundError) {
          setError(error.message);
          setIsConnectionError(false);
        } else {
          setError(error instanceof Error ? error.message : 'An unexpected error occurred');
          setIsConnectionError(true);
        }
      } finally {
        setIsLoading(false);
      }
    }
    fetchMystery();
  }, [docId]);

  const handleSelectSuspect = (suspectId: string) => {
    setDraftSelectedSuspectId(suspectId);
  };

  const handleConfirmAccusation = () => {
    if (!draftSelectedSuspectId || !currentMystery) return;

    setFinalAccusedSuspectId(draftSelectedSuspectId);
    const isCorrect = draftSelectedSuspectId === currentMystery.killerId;
    setIsCorrectGuess(isCorrect);
    setShowExplanation(true);

    // Scroll to explanation after a brief delay
    setTimeout(() => {
      const explanationElement = document.getElementById('explanation-section');
      if (explanationElement) {
        explanationElement.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100);
  };

  const resetDailyState = () => {
    setDraftSelectedSuspectId(null);
    setFinalAccusedSuspectId(null);
    setIsCorrectGuess(null);
    setShowExplanation(false);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4 md:p-8">
        <NewspaperContainer>
          <div className="text-center">
            <Loader2 className="h-16 w-16 text-primary animate-spin mx-auto" />
            <p className="mt-4 text-xl newspaper-headline text-primary">Unearthing the mystery...</p>
          </div>
        </NewspaperContainer>
      </div>
    );
  }

  if (error || !currentMystery) {
    return (
      <div className="min-h-screen">
        <NewspaperContainer>
          <Masthead
            title="THE DAILY DEDUCTION"
            tagline="A new enigma unfolds each day. Can you piece together the clues?"
          />

          <main>
            <NoMysteryAvailable
              error={error || undefined}
              isConnectionError={isConnectionError}
            />
          </main>

          <footer className="mt-12 pt-8 border-t border-primary/20 text-center">
            <p className="text-sm text-muted-foreground">
              &copy; {new Date().getFullYear()} The Daily Deduction. The truth is out there.
            </p>
          </footer>
        </NewspaperContainer>
      </div>
    );
  }
  
  const accusedSuspect = currentMystery.suspects.find(s => s.id === finalAccusedSuspectId);

  return (
    <div className="min-h-screen">
      <NewspaperContainer>
        <Masthead
          title="THE DAILY DEDUCTION"
          tagline="A new enigma unfolds each day. Can you piece together the clues?"
        />

        <ArticleLayout
          mainContent={
            <div className="space-y-6 sm:space-y-8">
              <DailyMysteryCard mystery={currentMystery} />

              {showExplanation && (
                <div id="explanation-section">
                  <CrimeExplanation
                    explanation={currentMystery.explanation}
                    killerName={currentMystery.suspects.find(s => s.id === currentMystery.killerId)?.name || 'The Culprit'}
                    isCorrectGuess={isCorrectGuess}
                    accusedName={accusedSuspect?.name}
                  />
                </div>
              )}
            </div>
          }
          sidebarContent={
            !finalAccusedSuspectId ? (
              <SuspectList
                suspects={currentMystery.suspects}
                onSelectSuspect={handleSelectSuspect}
                onConfirmAccusation={handleConfirmAccusation}
                selectedSuspectId={draftSelectedSuspectId}
                isAccusationMade={!!finalAccusedSuspectId}
              />
            ) : undefined
          }
        />

        <footer className="mt-12 pt-8 border-t border-primary/20 text-center">
          <p className="text-sm text-muted-foreground">
            &copy; {new Date().getFullYear()} The Daily Deduction. The truth is out there.
          </p>
        </footer>
      </NewspaperContainer>
    </div>
  );
}
